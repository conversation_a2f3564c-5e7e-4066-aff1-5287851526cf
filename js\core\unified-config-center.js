/**
 * @OTA_CORE 统一配置中心
 * 🏷️ 标签: @CONFIG_CENTER @UNIFIED_CONFIG
 * 📝 说明: 统一管理所有系统配置，支持环境变量、配置验证、热更新和配置继承
 * ⚠️ 警告: 核心配置管理，请勿重复开发
 */

(function() {
    'use strict';

    // 延迟获取依赖，确保加载顺序

    /**
     * @OTA_CORE 统一配置中心类
     * 管理所有系统配置的统一入口
     */
    class UnifiedConfigCenter {
        constructor() {
            this.logger = window.OTA.getService('logger');
            
            // 配置存储
            this.configs = new Map();
            this.watchers = new Map();
            this.validators = new Map();
            
            // 环境检测
            this.environment = this.detectEnvironment();
            
            // 配置元数据
            this.metadata = {
                version: '1.0.0',
                lastUpdate: Date.now(),
                loadedConfigs: new Set(),
                validationErrors: [],
                hotReloadEnabled: true
            };
            
            // 初始化默认配置
            this.initializeDefaultConfigs();

            // 初始化默认验证器
            this.initializeDefaultValidators();

            // 启动热更新监听
            this.startHotReloadWatcher();
            
            this.logger.log('✅ 统一配置中心已初始化', 'info', {
                environment: this.environment,
                configCount: this.configs.size
            });
        }

        /**
         * 检测运行环境
         * @returns {string} 环境名称
         */
        detectEnvironment() {
            // 检查URL
            const hostname = window.location?.hostname || '';
            if (hostname === 'localhost' || hostname === '127.0.0.1') {
                return 'development';
            }
            if (hostname.includes('test') || hostname.includes('staging')) {
                return 'testing';
            }
            return 'production';
        }

        /**
         * 初始化默认配置
         */
        initializeDefaultConfigs() {
            // API配置
            this.setConfig('api', {
                baseURL: 'https://gomyhire.com.my/api',
                timeout: this.environment === 'development' ? 30000 : 15000,
                retries: 3,
                rateLimit: {
                    enabled: this.environment === 'production',
                    maxRequests: 1000,
                    windowMs: 60000
                }
            });

            // Gemini AI配置已迁移到专用配置管理器
            // 参见: js/ai/gemini/core/config-manager.js

            // UI配置
            this.setConfig('ui', {
                theme: 'fluent-purple',
                animations: true,
                touchOptimized: true,
                minTouchTarget: 44,
                debounceDelay: 300
            });

            // 性能配置
            this.setConfig('performance', {
                cacheEnabled: true,
                cacheSize: this.environment === 'production' ? 10000 : 1000,
                cacheTTL: 30 * 60 * 1000, // 30分钟
                enableCompression: this.environment === 'production',
                enableMetrics: true
            });

            // 日志配置
            this.setConfig('logging', {
                level: this.environment === 'development' ? 'debug' : 'info',
                enableConsole: true,
                enableRemote: this.environment === 'production',
                maxLogSize: 1000,
                enablePerformanceLogging: true
            });

            // 功能开关
            this.setConfig('features', {
                multiOrderMode: true,
                imageUpload: true,
                currencyConversion: true,
                realtimeAnalysis: true,
                pagingService: true,
                orderHistory: true,
                smartDefaults: true
            });

            // 安全配置
            this.setConfig('security', {
                enableCSP: this.environment === 'production',
                tokenExpiry: 24 * 60 * 60 * 1000, // 24小时
                rememberMeExpiry: 7 * 24 * 60 * 60 * 1000, // 7天
                enableEncryption: this.environment === 'production'
            });
        }

        /**
         * 设置配置
         * @param {string} key - 配置键
         * @param {any} value - 配置值
         * @param {Object} options - 选项
         */
        setConfig(key, value, options = {}) {
            const oldValue = this.configs.get(key);
            
            // 验证配置
            if (this.validators.has(key)) {
                const validator = this.validators.get(key);
                const validationResult = validator(value);
                if (!validationResult.isValid) {
                    this.logger.logError(`配置验证失败 [${key}]`, validationResult.errors);
                    if (options.strict !== false) {
                        throw new Error(`配置验证失败: ${validationResult.errors.join(', ')}`);
                    }
                }
            }
            
            // 设置配置
            this.configs.set(key, {
                value,
                timestamp: Date.now(),
                environment: this.environment,
                metadata: options.metadata || {}
            });
            
            // 触发监听器
            if (this.watchers.has(key)) {
                const watchers = this.watchers.get(key);
                watchers.forEach(watcher => {
                    try {
                        watcher(value, oldValue?.value, key);
                    } catch (error) {
                        this.logger.logError(`配置监听器执行失败 [${key}]`, error);
                    }
                });
            }
            
            this.metadata.lastUpdate = Date.now();
            this.metadata.loadedConfigs.add(key);
            
            this.logger.log(`配置已更新: ${key}`, 'info', { 
                hasOldValue: !!oldValue,
                environment: this.environment 
            });
        }

        /**
         * 获取配置
         * @param {string} key - 配置键
         * @param {any} defaultValue - 默认值
         * @returns {any} 配置值
         */
        getConfig(key, defaultValue = null) {
            const config = this.configs.get(key);
            if (!config) {
                if (defaultValue !== null) {
                    return defaultValue;
                }
                this.logger.log(`配置不存在: ${key}`, 'warning');
                return null;
            }
            
            return config.value;
        }

        /**
         * 获取嵌套配置
         * @param {string} path - 配置路径 (如: 'api.timeout')
         * @param {any} defaultValue - 默认值
         * @returns {any} 配置值
         */
        getNestedConfig(path, defaultValue = null) {
            const keys = path.split('.');
            const rootKey = keys[0];
            const config = this.getConfig(rootKey);
            
            if (!config) {
                return defaultValue;
            }
            
            let value = config;
            for (let i = 1; i < keys.length; i++) {
                if (value && typeof value === 'object' && keys[i] in value) {
                    value = value[keys[i]];
                } else {
                    return defaultValue;
                }
            }
            
            return value;
        }

        /**
         * 监听配置变化
         * @param {string} key - 配置键
         * @param {Function} callback - 回调函数
         * @returns {Function} 取消监听函数
         */
        watchConfig(key, callback) {
            if (!this.watchers.has(key)) {
                this.watchers.set(key, new Set());
            }
            
            this.watchers.get(key).add(callback);
            
            // 返回取消监听函数
            return () => {
                const watchers = this.watchers.get(key);
                if (watchers) {
                    watchers.delete(callback);
                    if (watchers.size === 0) {
                        this.watchers.delete(key);
                    }
                }
            };
        }

        /**
         * 注册配置验证器
         * @param {string} key - 配置键
         * @param {Function} validator - 验证函数
         */
        registerValidator(key, validator) {
            this.validators.set(key, validator);
            this.logger.log(`已注册配置验证器: ${key}`, 'info');
        }

        /**
         * 初始化默认验证器
         */
        initializeDefaultValidators() {
            // API配置验证器
            this.registerValidator('api', (config) => {
                const errors = [];
                if (!config.baseURL || typeof config.baseURL !== 'string') {
                    errors.push('baseURL必须是有效的字符串');
                }
                if (!config.timeout || config.timeout < 1000) {
                    errors.push('timeout必须大于1000ms');
                }
                return { isValid: errors.length === 0, errors };
            });

            // Gemini配置验证器已迁移到专用配置管理器
            // 参见: js/ai/gemini/core/config-manager.js

            // 性能配置验证器
            this.registerValidator('performance', (config) => {
                const errors = [];
                if (config.cacheSize && config.cacheSize < 100) {
                    errors.push('cacheSize不能小于100');
                }
                if (config.cacheTTL && config.cacheTTL < 60000) {
                    errors.push('cacheTTL不能小于60秒');
                }
                return { isValid: errors.length === 0, errors };
            });
        }

        /**
         * 启动热更新监听
         */
        startHotReloadWatcher() {
            if (!this.metadata.hotReloadEnabled) {
                return;
            }
            
            // 监听环境变化
            const checkEnvironment = () => {
                const newEnv = this.detectEnvironment();
                if (newEnv !== this.environment) {
                    this.logger.log(`环境变化检测: ${this.environment} -> ${newEnv}`, 'info');
                    this.environment = newEnv;
                    this.reloadEnvironmentConfigs();
                }
            };
            
            // 每30秒检查一次环境变化
            setInterval(checkEnvironment, 30000);
        }

        /**
         * 重新加载环境相关配置
         */
        reloadEnvironmentConfigs() {
            this.logger.log('重新加载环境配置', 'info', { environment: this.environment });
            
            // 重新初始化默认配置
            this.initializeDefaultConfigs();
            
            // 触发全局配置变化事件
            if (this.watchers.has('*')) {
                const globalWatchers = this.watchers.get('*');
                globalWatchers.forEach(watcher => {
                    try {
                        watcher(this.environment, null, 'environment');
                    } catch (error) {
                        this.logger.logError('全局配置监听器执行失败', error);
                    }
                });
            }
        }

        /**
         * 获取所有配置
         * @returns {Object} 所有配置的快照
         */
        getAllConfigs() {
            const snapshot = {};
            for (const [key, config] of this.configs.entries()) {
                snapshot[key] = config.value;
            }
            return snapshot;
        }

        /**
         * 获取配置中心状态
         * @returns {Object} 状态信息
         */
        getStatus() {
            return {
                environment: this.environment,
                configCount: this.configs.size,
                watcherCount: this.watchers.size,
                validatorCount: this.validators.size,
                metadata: { ...this.metadata },
                loadedConfigs: Array.from(this.metadata.loadedConfigs)
            };
        }

        /**
         * 导出配置到JSON
         * @returns {string} JSON字符串
         */
        exportConfigs() {
            const exportData = {
                environment: this.environment,
                timestamp: Date.now(),
                configs: this.getAllConfigs(),
                metadata: this.metadata
            };
            return JSON.stringify(exportData, null, 2);
        }

        /**
         * 从JSON导入配置
         * @param {string} jsonData - JSON数据
         * @param {Object} options - 导入选项
         */
        importConfigs(jsonData, options = {}) {
            try {
                const data = JSON.parse(jsonData);
                
                if (options.validateEnvironment && data.environment !== this.environment) {
                    throw new Error(`环境不匹配: 期望 ${this.environment}, 实际 ${data.environment}`);
                }
                
                for (const [key, value] of Object.entries(data.configs)) {
                    this.setConfig(key, value, { strict: false });
                }
                
                this.logger.log('配置导入完成', 'success', {
                    importedCount: Object.keys(data.configs).length,
                    sourceEnvironment: data.environment
                });
                
            } catch (error) {
                this.logger.logError('配置导入失败', error);
                throw error;
            }
        }
    }

    // 创建全局实例
    const unifiedConfigCenter = new UnifiedConfigCenter();

    // 导出到全局作用域
    window.OTA = window.OTA || {};
    window.OTA.configCenter = unifiedConfigCenter;
    window.OTA.getConfigCenter = () => unifiedConfigCenter;

    // 向后兼容
    window.getConfigCenter = () => unifiedConfigCenter;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('configCenter', unifiedConfigCenter, '@OTA_CONFIG_CENTER');
        window.OTA.Registry.registerFactory('getConfigCenter', () => unifiedConfigCenter, '@OTA_CONFIG_CENTER_FACTORY');
    }

})();
